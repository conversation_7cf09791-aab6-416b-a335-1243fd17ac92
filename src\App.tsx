import { useCallback, useEffect, useState } from '@lynx-js/react'

import './App.css'

export function App(props: {
  onMounted?: () => void
}) {
  const [currentUser, setCurrentUser] = useState<string | null>(null)

  useEffect(() => {
    console.info('Cambodian Loan Management App - Starting...')
    props.onMounted?.()
  }, [])

  const handleLogin = useCallback(() => {
    setCurrentUser('admin')
  }, [])

  const handleLogout = useCallback(() => {
    setCurrentUser(null)
  }, [])

  if (!currentUser) {
    return (
      <view style={{
        flex: 1,
        backgroundColor: '#f8f9fa',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20
      }}>
        {/* Logo and Title */}
        <view style={{
          alignItems: 'center',
          marginBottom: 40
        }}>
          <view style={{
            width: 80,
            height: 80,
            backgroundColor: '#007bff',
            borderRadius: 40,
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 20
          }}>
            <text style={{
              color: 'white',
              fontSize: 32,
              fontWeight: 'bold'
            }}>
              ₹
            </text>
          </view>

          <text style={{
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333',
            textAlign: 'center',
            marginBottom: 8
          }}>
            ប្រព័ន្ធគ្រប់គ្រងកម្ចី
          </text>

          <text style={{
            fontSize: 16,
            color: '#666',
            textAlign: 'center'
          }}>
            Cambodian Loan Management System
          </text>
        </view>

        {/* Login Button */}
        <view
          style={{
            backgroundColor: '#007bff',
            borderRadius: 8,
            height: 48,
            width: 200,
            justifyContent: 'center',
            alignItems: 'center'
          }}
          bindtap={handleLogin}
        >
          <text style={{
            color: 'white',
            fontSize: 16,
            fontWeight: '600'
          }}>
            ចូលប្រើប្រាស់ / Login
          </text>
        </view>

        <text style={{
          fontSize: 12,
          color: '#666',
          textAlign: 'center',
          marginTop: 20
        }}>
          Demo: Tap to login as admin
        </text>
      </view>
    )
  }

  return (
    <view style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
      {/* Header */}
      <view style={{
        backgroundColor: '#007bff',
        paddingTop: 40,
        paddingBottom: 10,
        padding: 20,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <view>
          <text style={{
            color: 'white',
            fontSize: 18,
            fontWeight: 'bold'
          }}>
            ប្រព័ន្ធគ្រប់គ្រងកម្ចី
          </text>
          <text style={{
            color: 'rgba(255,255,255,0.8)',
            fontSize: 14
          }}>
            សួស្តី Admin
          </text>
        </view>

        <view
          style={{
            backgroundColor: 'rgba(255,255,255,0.2)',
            borderRadius: 8,
            padding: 8
          }}
          bindtap={handleLogout}
        >
          <text style={{
            color: 'white',
            fontSize: 14
          }}>
            ចាកចេញ
          </text>
        </view>
      </view>

      {/* Dashboard Content */}
      <scroll-view style={{ flex: 1 }}>
        <view style={{ padding: 20 }}>
          <text style={{
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333',
            marginBottom: 20
          }}>
            ផ្ទាំងគ្រប់គ្រង / Dashboard
          </text>

          {/* Stats Cards */}
          <view style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            marginBottom: 30
          }}>
            <StatCard
              title="កម្ចីសកម្ម"
              subtitle="Active Loans"
              value="156"
              color="#007bff"
            />
            <StatCard
              title="ចំនួនកម្ចីសរុប"
              subtitle="Total Amount"
              value="$2,450,000"
              color="#28a745"
            />
            <StatCard
              title="កម្ចីយឺតយ៉ាវ"
              subtitle="Overdue Loans"
              value="23"
              color="#dc3545"
            />
            <StatCard
              title="អតិថិជនថ្មី"
              subtitle="New Clients"
              value="12"
              color="#17a2b8"
            />
          </view>

          {/* Quick Actions */}
          <text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#333',
            marginBottom: 15
          }}>
            សកម្មភាពរហ័ស / Quick Actions
          </text>

          <view style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            marginBottom: 30
          }}>
            <ActionButton
              title="បន្ថែមអតិថិជន"
              subtitle="Add Client"
              icon="👤"
              color="#007bff"
            />
            <ActionButton
              title="បង្កើតកម្ចី"
              subtitle="New Loan"
              icon="💰"
              color="#28a745"
            />
            <ActionButton
              title="កត់ត្រាការទូទាត់"
              subtitle="Record Payment"
              icon="💳"
              color="#17a2b8"
            />
            <ActionButton
              title="មើលរបាយការណ៍"
              subtitle="View Reports"
              icon="📊"
              color="#6f42c1"
            />
          </view>

          {/* Recent Activity */}
          <text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#333',
            marginBottom: 15
          }}>
            សកម្មភាពថ្មីៗ / Recent Activity
          </text>

          <view style={{
            backgroundColor: 'white',
            borderRadius: 12,
            padding: 15
          }}>
            <ActivityItem
              title="កម្ចីថ្មីត្រូវបានអនុម័ត"
              subtitle="New loan approved - សុខ សុភា - $5,000"
              time="2h ago"
            />
            <ActivityItem
              title="ការទូទាត់ត្រូវបានកត់ត្រា"
              subtitle="Payment recorded - ចាន់ ដារា - $250"
              time="4h ago"
            />
            <ActivityItem
              title="អតិថិជនថ្មីបានចុះឈ្មោះ"
              subtitle="New client registered - លី មុនី"
              time="6h ago"
            />
          </view>
        </view>
      </scroll-view>
    </view>
  )
}

function StatCard({ title, subtitle, value, color }: {
  title: string
  subtitle: string
  value: string
  color: string
}) {
  return (
    <view style={{
      width: '48%',
      backgroundColor: 'white',
      borderRadius: 12,
      padding: 15,
      margin: '1%',
      borderLeftWidth: 4,
      borderLeftColor: color
    }}>
      <text style={{
        fontSize: 12,
        color: '#666',
        marginBottom: 5
      }}>
        {title}
      </text>
      <text style={{
        fontSize: 10,
        color: '#999',
        marginBottom: 5
      }}>
        {subtitle}
      </text>
      <text style={{
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333'
      }}>
        {value}
      </text>
    </view>
  )
}

function ActionButton({ title, subtitle, icon, color }: {
  title: string
  subtitle: string
  icon: string
  color: string
}) {
  return (
    <view
      style={{
        width: '48%',
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 15,
        margin: '1%',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: color
      }}
    >
      <text style={{ fontSize: 24, marginBottom: 8 }}>
        {icon}
      </text>
      <text style={{
        fontSize: 12,
        color: color,
        fontWeight: '600',
        textAlign: 'center',
        marginBottom: 4
      }}>
        {title}
      </text>
      <text style={{
        fontSize: 10,
        color: '#666',
        textAlign: 'center'
      }}>
        {subtitle}
      </text>
    </view>
  )
}

function ActivityItem({ title, subtitle, time }: {
  title: string
  subtitle: string
  time: string
}) {
  return (
    <view style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingTop: 10,
      paddingBottom: 10,
      borderBottomWidth: 1,
      borderBottomColor: '#f0f0f0'
    }}>
      <view style={{
        width: 8,
        height: 8,
        backgroundColor: '#007bff',
        borderRadius: 4,
        marginRight: 12
      }} />
      <view style={{ flex: 1 }}>
        <text style={{
          fontSize: 14,
          fontWeight: '600',
          color: '#333',
          marginBottom: 2
        }}>
          {title}
        </text>
        <text style={{
          fontSize: 12,
          color: '#666'
        }}>
          {subtitle}
        </text>
      </view>
      <text style={{
        fontSize: 12,
        color: '#999'
      }}>
        {time}
      </text>
    </view>
  )
}
