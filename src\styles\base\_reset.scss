// Modern CSS Reset with Khmer-specific optimizations

// ===== UNIVERSAL RESET =====
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

// ===== ROOT ELEMENT =====
html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  scroll-behavior: smooth;
}

// ===== BODY =====
body {
  font-family: $font-family-primary;
  font-size: $font-size-base;
  font-weight: $font-weight-normal;
  line-height: $line-height-normal;
  color: $gray-800;
  background-color: $gray-50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  
  // Khmer text optimization
  font-feature-settings: "kern" 1, "liga" 1;
  font-variant-ligatures: common-ligatures;
}

// ===== HEADINGS =====
h1, h2, h3, h4, h5, h6 {
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;
  color: $gray-900;
  margin-bottom: $spacing-4;
  
  // Khmer heading optimization
  @include khmer-text;
  @include text-shadow-soft;
}

h1 {
  font-size: $font-size-4xl;
  
  @include mobile-only {
    font-size: $font-size-3xl;
  }
}

h2 {
  font-size: $font-size-3xl;
  
  @include mobile-only {
    font-size: $font-size-2xl;
  }
}

h3 {
  font-size: $font-size-2xl;
  
  @include mobile-only {
    font-size: $font-size-xl;
  }
}

h4 {
  font-size: $font-size-xl;
  
  @include mobile-only {
    font-size: $font-size-lg;
  }
}

h5 {
  font-size: $font-size-lg;
}

h6 {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
}

// ===== PARAGRAPHS =====
p {
  margin-bottom: $spacing-4;
  
  &:last-child {
    margin-bottom: 0;
  }
}

// ===== LINKS =====
a {
  color: $primary;
  text-decoration: none;
  transition: color $transition-base;
  
  &:hover {
    color: darken($primary, 10%);
    text-decoration: underline;
  }
  
  &:focus {
    outline: 2px solid $primary;
    outline-offset: 2px;
  }
}

// ===== LISTS =====
ul, ol {
  margin-bottom: $spacing-4;
  padding-left: $spacing-6;
  
  &:last-child {
    margin-bottom: 0;
  }
}

li {
  margin-bottom: $spacing-1;
  
  &:last-child {
    margin-bottom: 0;
  }
}

// ===== IMAGES =====
img {
  max-width: 100%;
  height: auto;
  display: block;
}

// ===== FORMS =====
input,
textarea,
select,
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

input,
textarea,
select {
  &:focus {
    outline: none;
  }
}

// ===== TABLES =====
table {
  border-collapse: collapse;
  width: 100%;
}

th,
td {
  text-align: left;
  padding: $spacing-3;
  border-bottom: 1px solid $gray-200;
}

th {
  font-weight: $font-weight-semibold;
  color: $gray-700;
  background-color: $gray-50;
}

// ===== MEDIA =====
audio,
canvas,
iframe,
img,
svg,
video {
  vertical-align: middle;
}

// ===== HIDDEN ELEMENTS =====
[hidden] {
  display: none !important;
}

// ===== ACCESSIBILITY =====
.sr-only {
  @include visually-hidden;
}

// Focus styles for keyboard navigation
:focus-visible {
  outline: 2px solid $primary;
  outline-offset: 2px;
}

// ===== SCROLLBAR STYLING =====
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: $gray-100;
  border-radius: $border-radius-base;
}

::-webkit-scrollbar-thumb {
  background: $gray-300;
  border-radius: $border-radius-base;
  transition: background $transition-base;
  
  &:hover {
    background: $gray-400;
  }
}

// Firefox scrollbar
* {
  scrollbar-width: thin;
  scrollbar-color: $gray-300 $gray-100;
}

// ===== SELECTION =====
::selection {
  background-color: alpha($primary, 0.2);
  color: $gray-900;
}

::-moz-selection {
  background-color: alpha($primary, 0.2);
  color: $gray-900;
}

// ===== PRINT STYLES =====
@media print {
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  img {
    page-break-inside: avoid;
  }
  
  h2,
  h3,
  p {
    orphans: 3;
    widows: 3;
  }
  
  h2,
  h3 {
    page-break-after: avoid;
  }
}
