// Mixins for Khmer Loan Management App

// ===== GLASS MORPHISM MIXIN =====
@mixin glass-morphism($bg-opacity: 0.25, $blur: 16px, $border-opacity: 0.18) {
  background: rgba(255, 255, 255, $bg-opacity);
  backdrop-filter: blur($blur);
  -webkit-backdrop-filter: blur($blur);
  border: 1px solid rgba(255, 255, 255, $border-opacity);
}

@mixin glass-morphism-dark($bg-opacity: 0.25, $blur: 16px, $border-opacity: 0.18) {
  background: rgba(0, 0, 0, $bg-opacity);
  backdrop-filter: blur($blur);
  -webkit-backdrop-filter: blur($blur);
  border: 1px solid rgba(255, 255, 255, $border-opacity);
}

// ===== GRADIENT MIXINS =====
@mixin gradient-khmer-gold {
  background: $gradient-khmer-gold;
}

@mixin gradient-temple-blue {
  background: $gradient-temple-blue;
}

@mixin gradient-lotus-pink {
  background: $gradient-lotus-pink;
}

@mixin gradient-sunset {
  background: $gradient-sunset;
}

@mixin gradient-temple {
  background: $gradient-temple;
}

// ===== ANIMATION MIXINS =====
@mixin floating-animation($duration: 3s, $distance: 10px) {
  animation: floating-#{unique-id()} #{$duration} ease-in-out infinite;
  
  @keyframes floating-#{unique-id()} {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-#{$distance});
    }
  }
}

@mixin shimmer-effect($duration: 2s) {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    animation: shimmer #{$duration} infinite;
  }
  
  @keyframes shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }
}

@mixin pulse-animation($duration: 2s, $scale: 1.05) {
  animation: pulse-#{unique-id()} #{$duration} infinite;
  
  @keyframes pulse-#{unique-id()} {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale($scale);
    }
  }
}

// ===== HOVER EFFECTS =====
@mixin hover-lift($distance: 4px, $shadow: $shadow-lg) {
  transition: transform $transition-base, box-shadow $transition-base;
  
  &:hover {
    transform: translateY(-#{$distance});
    box-shadow: $shadow;
  }
}

@mixin hover-scale($scale: 1.05) {
  transition: transform $transition-base;
  
  &:hover {
    transform: scale($scale);
  }
}

@mixin hover-glow($color: $primary, $intensity: 0.5) {
  transition: box-shadow $transition-base;
  
  &:hover {
    box-shadow: 0 0 20px rgba($color, $intensity);
  }
}

// ===== RESPONSIVE MIXINS =====
@mixin mobile-only {
  @media (max-width: #{$breakpoint-sm - 1px}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: #{$breakpoint-md}) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

@mixin large-desktop-up {
  @media (min-width: #{$breakpoint-xl}) {
    @content;
  }
}

// ===== FLEXBOX MIXINS =====
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

// ===== TEXT MIXINS =====
@mixin text-shadow-soft($color: rgba(0, 0, 0, 0.1)) {
  text-shadow: 0 1px 3px $color;
}

@mixin text-shadow-strong($color: rgba(0, 0, 0, 0.3)) {
  text-shadow: 0 2px 4px $color;
}

@mixin text-gradient($gradient) {
  background: $gradient;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// ===== KHMER TEXT STYLING =====
@mixin khmer-text {
  font-family: $font-family-primary;
  line-height: $line-height-relaxed;
  letter-spacing: 0.025em;
}

// ===== BUTTON MIXINS =====
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-3 $spacing-6;
  border-radius: $border-radius-lg;
  border: none;
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  cursor: pointer;
  transition: all $transition-base;
  text-decoration: none;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

@mixin button-gradient($gradient, $hover-shadow) {
  @include button-base;
  background: $gradient;
  color: $white;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: $hover-shadow;
  }
  
  &:active {
    transform: translateY(0);
  }
}

// ===== CARD MIXINS =====
@mixin card-base {
  background: $white;
  border-radius: $border-radius-xl;
  padding: $spacing-6;
  box-shadow: $shadow-base;
  transition: all $transition-base;
}

@mixin card-glass {
  @include glass-morphism;
  border-radius: $border-radius-xl;
  padding: $spacing-6;
  transition: all $transition-base;
}

// ===== FORM MIXINS =====
@mixin input-base {
  width: 100%;
  padding: $spacing-3 $spacing-4;
  border: 2px solid $gray-200;
  border-radius: $border-radius-lg;
  font-size: $font-size-base;
  transition: all $transition-base;
  background: $white;
  
  &:focus {
    outline: none;
    border-color: $primary;
    box-shadow: 0 0 0 3px rgba($primary, 0.1);
  }
  
  &::placeholder {
    color: $gray-400;
  }
}

// ===== UTILITY MIXINS =====
@mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@mixin truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin aspect-ratio($width, $height) {
  position: relative;
  
  &::before {
    content: '';
    display: block;
    padding-top: percentage($height / $width);
  }
  
  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
