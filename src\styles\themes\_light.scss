// Light Theme for Khmer Loan Management App

:root,
[data-theme="light"] {
  // ===== SEMANTIC COLORS =====
  --color-primary: #{$temple-blue-500};
  --color-primary-hover: #{$temple-blue-600};
  --color-primary-light: #{$temple-blue-100};
  
  --color-secondary: #{$khmer-gold-500};
  --color-secondary-hover: #{$khmer-gold-600};
  --color-secondary-light: #{$khmer-gold-100};
  
  --color-accent: #{$lotus-pink-500};
  --color-accent-hover: #{$lotus-pink-600};
  --color-accent-light: #{$lotus-pink-100};
  
  --color-success: #{$success};
  --color-warning: #{$warning};
  --color-danger: #{$danger};
  --color-info: #{$info};
  
  // ===== BACKGROUND COLORS =====
  --bg-primary: #{$white};
  --bg-secondary: #{$gray-50};
  --bg-tertiary: #{$gray-100};
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  // ===== TEXT COLORS =====
  --text-primary: #{$gray-900};
  --text-secondary: #{$gray-700};
  --text-tertiary: #{$gray-600};
  --text-muted: #{$gray-500};
  --text-inverse: #{$white};
  
  // ===== BORDER COLORS =====
  --border-primary: #{$gray-200};
  --border-secondary: #{$gray-300};
  --border-focus: #{$temple-blue-500};
  
  // ===== SHADOW COLORS =====
  --shadow-color: rgba(0, 0, 0, 0.1);
  --shadow-color-strong: rgba(0, 0, 0, 0.15);
  
  // ===== GLASS MORPHISM =====
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-backdrop: blur(16px);
  
  // ===== GRADIENTS =====
  --gradient-primary: #{$gradient-temple-blue};
  --gradient-secondary: #{$gradient-khmer-gold};
  --gradient-accent: #{$gradient-lotus-pink};
  --gradient-sunset: #{$gradient-sunset};
  --gradient-temple: #{$gradient-temple};
  
  // ===== COMPONENT SPECIFIC =====
  --navbar-bg: rgba(255, 255, 255, 0.9);
  --sidebar-bg: rgba(255, 255, 255, 0.95);
  --card-bg: #{$white};
  --input-bg: #{$white};
  --button-bg: #{$white};
  
  // ===== KHMER PATTERN BACKGROUND =====
  --pattern-primary: rgba(#{red($khmer-gold-200)}, #{green($khmer-gold-200)}, #{blue($khmer-gold-200)}, 0.1);
  --pattern-secondary: rgba(#{red($temple-blue-200)}, #{green($temple-blue-200)}, #{blue($temple-blue-200)}, 0.1);
  --pattern-tertiary: rgba(#{red($lotus-pink-200)}, #{green($lotus-pink-200)}, #{blue($lotus-pink-200)}, 0.05);
}

// ===== LIGHT THEME SPECIFIC STYLES =====
.theme-light {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  color: var(--text-primary);
  
  // Enhanced background pattern for light theme
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      // Khmer temple silhouettes
      radial-gradient(circle at 20% 20%, var(--pattern-primary) 0%, transparent 40%),
      radial-gradient(circle at 80% 80%, var(--pattern-secondary) 0%, transparent 40%),
      radial-gradient(circle at 40% 60%, var(--pattern-tertiary) 0%, transparent 30%),
      // Lotus patterns
      radial-gradient(ellipse at 60% 20%, var(--pattern-primary) 0%, transparent 25%),
      radial-gradient(ellipse at 20% 80%, var(--pattern-secondary) 0%, transparent 25%);
    background-size: 
      800px 800px,
      600px 600px,
      400px 400px,
      300px 300px,
      350px 350px;
    background-position: 
      0 0,
      200px 200px,
      400px 100px,
      100px 300px,
      500px 400px;
    animation: float-pattern 30s ease-in-out infinite;
    pointer-events: none;
    z-index: -2;
  }
  
  // Floating decorative elements
  .floating-decoration {
    position: fixed;
    pointer-events: none;
    z-index: -1;
    
    &.decoration-1 {
      top: 10%;
      left: 5%;
      width: 20px;
      height: 20px;
      background: var(--pattern-primary);
      border-radius: 50%;
      animation: floating 8s ease-in-out infinite, twinkle 3s ease-in-out infinite;
    }
    
    &.decoration-2 {
      top: 30%;
      right: 10%;
      width: 15px;
      height: 15px;
      background: var(--pattern-secondary);
      border-radius: 50%;
      animation: floating 6s ease-in-out infinite 1s, twinkle 4s ease-in-out infinite 1s;
    }
    
    &.decoration-3 {
      bottom: 20%;
      left: 15%;
      width: 25px;
      height: 25px;
      background: var(--pattern-tertiary);
      border-radius: 50%;
      animation: floating 10s ease-in-out infinite 2s, twinkle 5s ease-in-out infinite 2s;
    }
    
    &.decoration-4 {
      bottom: 40%;
      right: 20%;
      width: 18px;
      height: 18px;
      background: var(--pattern-primary);
      border-radius: 50%;
      animation: floating 7s ease-in-out infinite 3s, twinkle 3.5s ease-in-out infinite 3s;
    }
  }
}

// ===== COMPONENT OVERRIDES FOR LIGHT THEME =====
.theme-light {
  .navbar {
    background: var(--navbar-bg);
    backdrop-filter: var(--glass-backdrop);
    border-bottom: 1px solid var(--border-primary);
  }
  
  .sidebar {
    background: var(--sidebar-bg);
    backdrop-filter: var(--glass-backdrop);
    border-right: 1px solid var(--border-primary);
  }
  
  .card {
    background: var(--card-bg);
    border: 1px solid var(--border-primary);
    box-shadow: 0 4px 6px var(--shadow-color);
  }
  
  .card-glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
  }
  
  .form-control {
    background: var(--input-bg);
    border: 2px solid var(--border-primary);
    color: var(--text-primary);
    
    &:focus {
      border-color: var(--border-focus);
      box-shadow: 0 0 0 3px rgba(#{red($temple-blue-500)}, #{green($temple-blue-500)}, #{blue($temple-blue-500)}, 0.1);
    }
    
    &::placeholder {
      color: var(--text-muted);
    }
  }
  
  .btn {
    &.btn-primary {
      background: var(--gradient-primary);
      color: var(--text-inverse);
      border: none;
    }
    
    &.btn-secondary {
      background: var(--gradient-secondary);
      color: var(--text-inverse);
      border: none;
    }
    
    &.btn-accent {
      background: var(--gradient-accent);
      color: var(--text-inverse);
      border: none;
    }
  }
  
  .badge {
    &.badge-primary {
      background: var(--gradient-primary);
      color: var(--text-inverse);
    }
    
    &.badge-secondary {
      background: var(--gradient-secondary);
      color: var(--text-inverse);
    }
    
    &.badge-accent {
      background: var(--gradient-accent);
      color: var(--text-inverse);
    }
  }
  
  .nav-link {
    color: var(--text-secondary);
    
    &:hover {
      color: var(--color-primary);
      background: var(--color-primary-light);
    }
    
    &.nav-link-active {
      color: var(--color-primary);
      background: var(--color-primary-light);
    }
  }
  
  .text-primary { color: var(--text-primary); }
  .text-secondary { color: var(--text-secondary); }
  .text-tertiary { color: var(--text-tertiary); }
  .text-muted { color: var(--text-muted); }
  .text-inverse { color: var(--text-inverse); }
  
  .bg-primary { background-color: var(--bg-primary); }
  .bg-secondary { background-color: var(--bg-secondary); }
  .bg-tertiary { background-color: var(--bg-tertiary); }
  
  .border-primary { border-color: var(--border-primary); }
  .border-secondary { border-color: var(--border-secondary); }
  .border-focus { border-color: var(--border-focus); }
}

// ===== LIGHT THEME ANIMATIONS =====
@keyframes float-pattern {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
  25% {
    transform: translate(30px, -20px) rotate(1deg) scale(1.05);
  }
  50% {
    transform: translate(-20px, 30px) rotate(-1deg) scale(0.95);
  }
  75% {
    transform: translate(20px, -10px) rotate(0.5deg) scale(1.02);
  }
}

// ===== ACCESSIBILITY ENHANCEMENTS =====
@media (prefers-reduced-motion: reduce) {
  .theme-light {
    &::before {
      animation: none;
    }
    
    .floating-decoration {
      animation: none;
    }
  }
  
  .animate-floating,
  .animate-pulse,
  .animate-shimmer {
    animation: none;
  }
}

// ===== HIGH CONTRAST MODE =====
@media (prefers-contrast: high) {
  .theme-light {
    --border-primary: #{$gray-400};
    --border-secondary: #{$gray-500};
    --text-primary: #{$black};
    --text-secondary: #{$gray-800};
    --shadow-color: rgba(0, 0, 0, 0.3);
  }
}
